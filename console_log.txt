browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🌐 [SSR] Main domain detected - no subdomain 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:92 🌐 Main domain detected - no subdomain
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
success.vue:173 🔍 Fetching school data for session: cs_test_b1I2k93jdoU7MlmnhBkaeo7UW23Ygf8C7ksUABjtGGMScul2fsSQwhvfQK
success.vue:175 Fetch finished loading: POST "http://localhost:3000/api/stripe/get-session-data".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
fetchSchoolData @ success.vue:175
(anonymous) @ success.vue:204
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=40128521:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=40128521:382
flushJobs @ runtime-core.esm-bundler.js?v=40128521:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=40128521:333
resolve @ runtime-core.esm-bundler.js?v=40128521:7164
resolve @ runtime-core.esm-bundler.js?v=40128521:7171
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
success.vue:182 ✅ School data loaded: Proxy(Object) {schoolCode: 'xba1223', schoolName: 'SK Tiong Widu 2', schoolAddress: '', adminEmail: '<EMAIL>', adminFirstName: 'COURTNEY', …}
