<template>
  <div>
    <!-- Progress Bar -->
    <RegistrationProgress :current-step="1" />

    <!-- Main Content -->
    <section class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
          <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
            Buat Aka<PERSON>
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Langkah 1: Pendaftaran akaun
          </p>
        </div>

        <!-- Registration Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <PERSON><PERSON><PERSON> *
              </label>
              <input id="email" v-model="formData.email" type="email" required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="<EMAIL>" :disabled="loading || isGoogleUser" />
            </div>

            <!-- Password Field (hidden for Google users) -->
            <div v-if="!isGoogleUser">
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Kata Laluan *
              </label>
              <input id="password" v-model="formData.password" type="password" required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Masukkan kata laluan" :disabled="loading" />
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Minimum 6 aksara
              </p>
            </div>

            <!-- Selected Plan Display -->
            <div v-if="selectedPlan" class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
                Pelan Dipilih
              </h4>
              <div class="text-sm text-blue-700 dark:text-blue-300">
                {{ formatPlanName(selectedPlan) }}
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage"
              class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <p class="text-sm text-red-600 dark:text-red-400">{{ errorMessage }}</p>
            </div>

            <!-- Success Message -->
            <div v-if="successMessage"
              class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
              <p class="text-sm text-green-600 dark:text-green-400">{{ successMessage }}</p>
            </div>

            <!-- Google OAuth Loading State -->
            <div v-if="loading && isGoogleUser"
              class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
              <div class="flex items-center">
                <Icon name="heroicons:arrow-path-20-solid" class="animate-spin h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <p class="text-sm font-medium text-blue-900 dark:text-blue-200">Menyediakan akaun anda...</p>
                  <p class="text-xs text-blue-700 dark:text-blue-300 mt-1">Sila tunggu sebentar, kami sedang menyediakan
                    maklumat akaun anda.</p>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" :disabled="loading"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              <Icon v-if="loading" name="heroicons:arrow-path-20-solid" class="animate-spin -ml-1 mr-3 h-5 w-5" />
              {{ loading ? 'Memproses...' : 'Seterusnya' }}
            </button>

            <!-- Divider -->
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">atau</span>
              </div>
            </div>

            <!-- Google Sign-In Button -->
            <button type="button" @click="handleGoogleSignIn" :disabled="loading"
              class="w-full flex justify-center items-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              <Icon name="logos:google-icon" class="w-5 h-5 mr-3" />
              Daftar dengan Google
            </button>
          </form>
        </div>

        <!-- Footer Links -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Sudah mempunyai akaun?
            <NuxtLink to="/login" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
              Log masuk di sini
            </NuxtLink>
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Reactive data
const formData = ref({
  email: '',
  password: ''
})

const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const isGoogleUser = ref(false)

// Get selected plan from query params
const route = useRoute()
const selectedPlan = ref(route.query.plan as string || 'professional')

// Check if this is a Google sign-in return
const isGoogleReturn = ref(!!route.query.google)

// Supabase client
const { client: supabase } = useSupabase()

// Handle Google sign-in return
onMounted(async () => {
  if (isGoogleReturn.value) {
    console.log('Google sign-in return detected')

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()

    if (user) {
      console.log('Google user authenticated:', user.email)

      // Set form data from Google user
      formData.value.email = user.email || ''
      isGoogleUser.value = true

      // Store pre-billing data for Google user
      try {
        // Show loading state immediately
        loading.value = true

        await storePreBillingData(user.id, true)
        console.log('Pre-billing data stored for Google user')

        // Verify session token was stored
        const storedToken = sessionStorage.getItem('registration_session_token')
        console.log('Session token stored:', storedToken)

        // Redirect to next phase immediately - no artificial delay needed
        console.log('Redirecting Google user to maklumat page')
        await navigateTo('/daftar/maklumat')
      } catch (error) {
        console.error('Error storing pre-billing data for Google user:', error)
        errorMessage.value = 'Ralat semasa menyimpan data pendaftaran. Sila cuba lagi.'
        loading.value = false
      }
    }
  }
})

// Helper function to format plan names
const formatPlanName = (plan: string) => {
  const planNames: Record<string, string> = {
    basic: 'Pelan Asas - RM99/bulan',
    professional: 'Pelan Profesional - RM199/bulan',
    enterprise: 'Pelan Perusahaan - RM399/bulan'
  }
  return planNames[plan] || plan
}

// Generate session token
const generateSessionToken = () => {
  return crypto.randomUUID()
}

// Handle traditional email/password registration
const handleSubmit = async () => {
  console.log('=== REGISTRATION ATTEMPT STARTED ===', new Date().toISOString())

  if (isGoogleUser.value) {
    // If Google user, proceed to next phase
    console.log('Google user detected, proceeding to next phase')
    await proceedToNextPhase()
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    // Validate form
    if (!formData.value.email || !formData.value.password) {
      throw new Error('Sila isi semua medan yang diperlukan')
    }

    if (formData.value.password.length < 6) {
      throw new Error('Kata laluan mestilah sekurang-kurangnya 6 aksara')
    }

    // Standard SaaS registration - simple and clean
    console.log('=== STANDARD REGISTRATION FLOW ===')

    // Try to create new user account (no email confirmation required)
    const { data, error } = await supabase.auth.signUp({
      email: formData.value.email,
      password: formData.value.password
    })

    if (error) {
      if (error.message.includes('already registered')) {
        // Standard SaaS approach - direct user to sign in
        throw new Error('Emel ini telah didaftarkan. Sila log masuk ke akaun anda atau gunakan emel lain.')
      }
      throw new Error(error.message)
    }

    if (data.user) {
      console.log('New user created successfully:', data.user.id)

      // Store initial registration data (email is automatically verified)
      await storePreBillingData(data.user.id, false)

      // Redirect directly to school information phase
      console.log('Redirecting to school information phase')
      await navigateTo('/daftar/maklumat')
    }

  } catch (error: any) {
    console.error('Registration error:', error)
    console.error('Error message:', error.message)
    console.error('Error stack:', error.stack)
    errorMessage.value = error.message || 'Ralat semasa pendaftaran. Sila cuba lagi.'
  } finally {
    loading.value = false
  }
}

// Handle Google sign-in
const handleGoogleSignIn = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/daftar?google=true`
      }
    })

    if (error) {
      throw new Error(error.message)
    }

  } catch (error: any) {
    console.error('Google sign-in error:', error)
    errorMessage.value = error.message || 'Ralat semasa log masuk dengan Google. Sila cuba lagi.'
    loading.value = false
  }
}

// Store pre-billing data
const storePreBillingData = async (userId: string, isGoogle: boolean) => {
  const sessionToken = generateSessionToken()

  // Store in sessionStorage
  sessionStorage.setItem('registration_session_token', sessionToken)

  // Store in database
  // Note: Setting user_id to null temporarily due to foreign key constraint issue
  // The foreign key references a 'users' table that doesn't exist in current schema
  const { error } = await supabase
    .from('pre_billing')
    .insert({
      session_token: sessionToken,
      user_id: null, // Temporarily set to null due to FK constraint issue
      admin_email: formData.value.email,
      is_google_signup: isGoogle,
      email_verified: true, // Email verification disabled - all emails are considered verified
      selected_plan: selectedPlan.value,
      phase_completed: 1
    })

  if (error) {
    console.error('Error storing pre-billing data:', error)
    throw new Error('Ralat menyimpan data pendaftaran')
  }
}

// Proceed to next phase (for Google users)
const proceedToNextPhase = async () => {
  await navigateTo('/daftar/maklumat')
}

// Set page head
useHead({
  title: 'Daftar Akaun Sekolah - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Daftar akaun sekolah baharu dengan RPHMate. Mulakan percubaan percuma 30 hari.'
    }
  ]
})
</script>
